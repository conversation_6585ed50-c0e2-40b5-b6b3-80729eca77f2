from langchain_core.messages import AIMessage
import time
import json
from ..utils.prompt_manager import PromptManager


def create_bear_researcher(llm, memory, config=None):
    def bear_node(state) -> dict:
        investment_debate_state = state["investment_debate_state"]
        history = investment_debate_state.get("history", "")
        bear_history = investment_debate_state.get("bear_history", "")

        current_response = investment_debate_state.get("current_response", "")
        market_research_report = state["market_report"]
        sentiment_report = state["sentiment_report"]
        news_report = state["news_report"]
        fundamentals_report = state["fundamentals_report"]

        curr_situation = f"{market_research_report}\n\n{sentiment_report}\n\n{news_report}\n\n{fundamentals_report}"
        past_memories = memory.get_memories(curr_situation, n_matches=2)

        past_memory_str = ""
        for i, rec in enumerate(past_memories, 1):
            past_memory_str += rec["recommendation"] + "\n\n"

        # 初始化提示词管理器
        language = config.get("report_language", "chinese") if config else "chinese"
        prompt_manager = PromptManager(language)

        # 使用提示词管理器格式化提示词
        prompt = prompt_manager.format_researcher_prompt(
            "bear_researcher",
            market_research_report,
            sentiment_report,
            news_report,
            fundamentals_report,
            history,
            current_response,
            past_memory_str
        )

        response = llm.invoke(prompt)

        argument = f"Bear Analyst: {response.content}"

        new_investment_debate_state = {
            "history": history + "\n" + argument,
            "bear_history": bear_history + "\n" + argument,
            "bull_history": investment_debate_state.get("bull_history", ""),
            "current_response": argument,
            "count": investment_debate_state["count"] + 1,
        }

        return {"investment_debate_state": new_investment_debate_state}

    return bear_node
