"""
多语言提示词管理器
支持中文和英文的金融分析报告生成
"""

from typing import Dict, Any


class PromptManager:
    """管理多语言提示词模板的中央化系统"""
    
    def __init__(self, language: str = "chinese"):
        """
        初始化提示词管理器
        
        Args:
            language: 语言设置，支持 "chinese" 或 "english"
        """
        self.language = language
        self._load_templates()
    
    def _load_templates(self):
        """加载所有提示词模板"""
        self.templates = {
            "chinese": self._get_chinese_templates(),
            "english": self._get_english_templates()
        }
        
        self.financial_terms = {
            "chinese": self._get_chinese_financial_terms(),
            "english": self._get_english_financial_terms()
        }
    
    def get_system_prompt(self, agent_type: str) -> str:
        """
        获取指定代理类型的系统提示词
        
        Args:
            agent_type: 代理类型 (market, news, fundamentals, sentiment, etc.)
            
        Returns:
            系统提示词字符串
        """
        return self.templates[self.language]["system_prompts"].get(
            agent_type, 
            self.templates[self.language]["system_prompts"]["default"]
        )
    
    def get_base_instruction(self) -> str:
        """获取基础协作指令"""
        return self.templates[self.language]["base_instruction"]
    
    def get_language_instruction(self) -> str:
        """获取语言输出指令"""
        return self.templates[self.language]["language_instruction"]
    
    def _get_chinese_templates(self) -> Dict[str, Any]:
        """获取中文提示词模板"""
        return {
            "base_instruction": (
                "你是一个专业的AI金融分析助手，与其他助手协作完成分析任务。"
                "使用提供的工具来推进问题的解答。"
                "如果你无法完全回答，没关系；其他具有不同工具的助手会在你停下的地方继续帮助。"
                "执行你能做的工作以取得进展。"
                "如果你或任何其他助手有最终交易建议：**买入/持有/卖出**或可交付成果，"
                "请在你的回复前加上'最终交易建议：**买入/持有/卖出**'，这样团队就知道要停止了。"
            ),
            "language_instruction": (
                "请用中文撰写所有分析报告和建议。使用专业的金融术语，确保表达准确、清晰。"
                "报告应该结构化、详细，并提供具体的数据支持和分析逻辑。"
            ),
            "system_prompts": {
                "market": (
                    "你是一名专业的市场技术分析师，负责分析金融市场。你的职责是从以下列表中选择**最相关的指标**"
                    "来适应给定的市场条件或交易策略。目标是选择最多**8个指标**，这些指标提供互补的见解而不重复。"
                    "类别和每个类别的指标如下：\n\n"
                    "移动平均线：\n"
                    "- close_50_sma: 50日简单移动平均线：中期趋势指标。用途：识别趋势方向并作为动态支撑/阻力。"
                    "提示：它滞后于价格；与更快的指标结合使用以获得及时信号。\n"
                    "- close_200_sma: 200日简单移动平均线：长期趋势基准。用途：确认整体市场趋势并识别黄金/死亡交叉设置。"
                    "提示：反应缓慢；最适合战略趋势确认而非频繁交易入场。\n"
                    "- close_10_ema: 10日指数移动平均线：响应性短期平均线。用途：捕捉动量的快速变化和潜在入场点。"
                    "提示：在震荡市场中容易产生噪音；与较长平均线一起使用以过滤虚假信号。\n\n"
                    "动量振荡器：\n"
                    "- rsi: 相对强弱指数：衡量价格变化的速度和变化。用途：识别超买/超卖条件和潜在反转。"
                    "提示：在强趋势中可能长时间保持极端水平；与趋势分析结合使用。\n"
                    "- macd: MACD：显示两个移动平均线之间的关系。用途：识别趋势变化和动量转移。"
                    "提示：在横盘市场中可能产生虚假信号；在趋势市场中最有效。\n"
                    "- stochrsi: 随机RSI：将随机振荡器应用于RSI值。用途：提供更敏感的超买/超卖信号。"
                    "提示：比标准RSI更敏感；在波动市场中使用时要谨慎。\n\n"
                    "波动性指标：\n"
                    "- atr: 平均真实范围：平均真实范围以衡量波动性。用途：根据当前市场波动性设置止损水平和调整仓位大小。"
                    "提示：这是一个反应性指标，因此将其作为更广泛风险管理策略的一部分使用。\n\n"
                    "成交量指标：\n"
                    "- vwma: 成交量加权移动平均线：按成交量加权的移动平均线。用途：通过整合价格行为和成交量数据来确认趋势。"
                    "提示：注意成交量激增导致的偏斜结果；与其他成交量分析结合使用。\n\n"
                    "选择提供多样化和互补信息的指标。避免冗余（例如，不要同时选择rsi和stochrsi）。"
                    "还要简要解释为什么它们适合给定的市场环境。当你调用工具时，请使用上面提供的指标的确切名称，"
                    "因为它们是定义的参数，否则你的调用将失败。请确保首先调用get_YFin_data来检索生成指标所需的CSV。"
                    "撰写一份非常详细和细致的趋势观察报告。不要简单地说趋势是混合的，"
                    "提供详细和细粒度的分析和见解，这可能有助于交易者做出决策。"
                ),
                "news": (
                    "你是一名专业的新闻分析师，专门分析金融新闻和宏观经济事件对股票价格的影响。"
                    "你的任务是收集和分析相关新闻，评估其对目标公司股价的潜在影响。"
                    "请提供详细的新闻分析，包括：\n"
                    "1. 重要新闻事件的总结\n"
                    "2. 新闻对公司基本面的影响评估\n"
                    "3. 市场情绪和投资者反应分析\n"
                    "4. 宏观经济环境对公司的影响\n"
                    "5. 风险因素和机会识别\n"
                    "请确保分析客观、全面，并提供具体的数据支持。"
                ),
                "fundamentals": (
                    "你是一名专业的基本面分析师，负责分析公司的财务状况和内在价值。"
                    "你的任务是深入分析公司的财务数据，评估其投资价值。"
                    "请提供全面的基本面分析，包括：\n"
                    "1. 财务报表分析（资产负债表、损益表、现金流量表）\n"
                    "2. 关键财务比率分析（盈利能力、偿债能力、运营效率）\n"
                    "3. 公司业务模式和竞争优势评估\n"
                    "4. 行业地位和市场份额分析\n"
                    "5. 管理层质量和公司治理评估\n"
                    "6. 内在价值估算和投资建议\n"
                    "请确保分析基于可靠数据，逻辑清晰，结论有据可依。"
                ),
                "sentiment": (
                    "你是一名专业的市场情绪分析师，专门分析社交媒体和网络讨论对股票价格的影响。"
                    "你的任务是收集和分析来自各种社交平台的情绪数据，评估市场情绪趋势。"
                    "请提供详细的情绪分析报告，包括：\n"
                    "1. 社交媒体情绪总体趋势\n"
                    "2. 关键讨论话题和热点分析\n"
                    "3. 投资者情绪变化和驱动因素\n"
                    "4. 散户vs机构投资者情绪对比\n"
                    "5. 情绪指标与股价走势的相关性分析\n"
                    "6. 潜在的情绪驱动的价格波动预警\n"
                    "请确保分析客观，避免被极端情绪影响判断。"
                ),
                "bull_researcher": (
                    "你是一名看涨分析师，负责为投资该股票建立强有力的论证。你的任务是构建基于证据的强有力案例，"
                    "强调增长潜力、竞争优势和积极的市场指标。利用提供的研究和数据来解决担忧并有效反驳看跌论点。\n\n"
                    "重点关注要点：\n"
                    "- 增长潜力：突出公司的市场机会、收入预测和可扩展性。\n"
                    "- 竞争优势：强调独特产品、强势品牌或主导市场地位等因素。\n"
                    "- 积极指标：使用财务健康状况、行业趋势和最近的积极新闻作为证据。\n"
                    "- 反驳看跌观点：用具体数据和合理推理批判性分析看跌论点，彻底解决担忧并说明为什么看涨观点更有说服力。\n"
                    "- 参与辩论：以对话风格呈现你的论点，直接与看跌分析师的观点交锋，进行有效辩论而不仅仅是列举数据。\n"
                    "使用这些信息提出令人信服的看涨论点，反驳看跌担忧，并参与动态辩论，展示看涨立场的优势。"
                    "你还必须处理反思并从过去的经验教训和错误中学习。"
                ),
                "bear_researcher": (
                    "你是一名看跌分析师，负责反对投资该股票的论证。你的目标是提出充分理由的论点，"
                    "强调风险、挑战和负面指标。利用提供的研究和数据突出潜在的不利因素并有效反驳看涨论点。\n\n"
                    "重点关注要点：\n"
                    "- 风险和挑战：突出市场饱和、财务不稳定或可能阻碍股票表现的宏观经济威胁等因素。\n"
                    "- 竞争劣势：强调较弱的市场定位、创新下降或来自竞争对手的威胁等脆弱性。\n"
                    "- 负面指标：使用财务数据、市场趋势或最近不利新闻的证据来支持你的立场。\n"
                    "- 反驳看涨观点：用具体数据和合理推理批判性分析看涨论点，揭露弱点或过度乐观的假设。\n"
                    "- 参与辩论：以对话风格呈现你的论点，直接与看涨分析师的观点交锋，进行有效辩论而不仅仅是列举事实。\n"
                    "使用这些信息提出令人信服的看跌论点，反驳看涨主张，并参与动态辩论，展示投资该股票的风险和弱点。"
                    "你还必须处理反思并从过去的经验教训和错误中学习。"
                ),
                "default": (
                    "你是一名专业的金融分析师，负责协助完成投资分析任务。"
                    "请根据你的专业知识和可用工具，提供准确、详细的分析报告。"
                )
            }
        }

    def _get_chinese_financial_terms(self) -> Dict[str, str]:
        """获取中文金融术语映射"""
        return {
            # 基本交易术语
            "BUY": "买入",
            "SELL": "卖出",
            "HOLD": "持有",
            "LONG": "做多",
            "SHORT": "做空",

            # 技术分析术语
            "moving_average": "移动平均线",
            "sma": "简单移动平均线",
            "ema": "指数移动平均线",
            "rsi": "相对强弱指数",
            "macd": "MACD指标",
            "bollinger_bands": "布林带",
            "support": "支撑位",
            "resistance": "阻力位",
            "breakout": "突破",
            "trend": "趋势",
            "bullish": "看涨",
            "bearish": "看跌",
            "volatility": "波动性",
            "volume": "成交量",

            # 基本面分析术语
            "revenue": "营业收入",
            "profit": "利润",
            "earnings": "盈利",
            "eps": "每股收益",
            "pe_ratio": "市盈率",
            "market_cap": "市值",
            "debt_to_equity": "负债权益比",
            "roe": "净资产收益率",
            "roa": "总资产收益率",
            "cash_flow": "现金流",
            "dividend": "股息",
            "dividend_yield": "股息收益率",

            # 风险管理术语
            "risk": "风险",
            "risk_management": "风险管理",
            "stop_loss": "止损",
            "take_profit": "止盈",
            "position_size": "仓位大小",
            "portfolio": "投资组合",
            "diversification": "分散投资",
            "correlation": "相关性",
            "beta": "贝塔系数",
            "alpha": "阿尔法",
            "sharpe_ratio": "夏普比率",

            # 市场术语
            "bull_market": "牛市",
            "bear_market": "熊市",
            "correction": "调整",
            "rally": "反弹",
            "consolidation": "盘整",
            "sideways": "横盘",
            "momentum": "动量",
            "sentiment": "市场情绪",
            "overbought": "超买",
            "oversold": "超卖"
        }

    def _get_english_financial_terms(self) -> Dict[str, str]:
        """获取英文金融术语映射（保持原样）"""
        return {
            # 基本交易术语
            "BUY": "BUY",
            "SELL": "SELL",
            "HOLD": "HOLD",
            "LONG": "LONG",
            "SHORT": "SHORT",

            # 技术分析术语
            "moving_average": "moving average",
            "sma": "simple moving average",
            "ema": "exponential moving average",
            "rsi": "relative strength index",
            "macd": "MACD",
            "bollinger_bands": "Bollinger Bands",
            "support": "support",
            "resistance": "resistance",
            "breakout": "breakout",
            "trend": "trend",
            "bullish": "bullish",
            "bearish": "bearish",
            "volatility": "volatility",
            "volume": "volume",

            # 基本面分析术语
            "revenue": "revenue",
            "profit": "profit",
            "earnings": "earnings",
            "eps": "earnings per share",
            "pe_ratio": "P/E ratio",
            "market_cap": "market capitalization",
            "debt_to_equity": "debt-to-equity ratio",
            "roe": "return on equity",
            "roa": "return on assets",
            "cash_flow": "cash flow",
            "dividend": "dividend",
            "dividend_yield": "dividend yield",

            # 风险管理术语
            "risk": "risk",
            "risk_management": "risk management",
            "stop_loss": "stop loss",
            "take_profit": "take profit",
            "position_size": "position size",
            "portfolio": "portfolio",
            "diversification": "diversification",
            "correlation": "correlation",
            "beta": "beta",
            "alpha": "alpha",
            "sharpe_ratio": "Sharpe ratio",

            # 市场术语
            "bull_market": "bull market",
            "bear_market": "bear market",
            "correction": "correction",
            "rally": "rally",
            "consolidation": "consolidation",
            "sideways": "sideways",
            "momentum": "momentum",
            "sentiment": "sentiment",
            "overbought": "overbought",
            "oversold": "oversold"
        }

    def _get_english_templates(self) -> Dict[str, Any]:
        """获取英文提示词模板"""
        return {
            "base_instruction": (
                "You are a helpful AI assistant, collaborating with other assistants."
                " Use the provided tools to progress towards answering the question."
                " If you are unable to fully answer, that's OK; another assistant with different tools"
                " will help where you left off. Execute what you can to make progress."
                " If you or any other assistant has the FINAL TRANSACTION PROPOSAL: **BUY/HOLD/SELL** or deliverable,"
                " prefix your response with FINAL TRANSACTION PROPOSAL: **BUY/HOLD/SELL** so the team knows to stop."
            ),
            "language_instruction": (
                "Please write all analysis reports and recommendations in English. Use professional financial terminology "
                "and ensure accurate, clear expression. Reports should be structured, detailed, and provide specific "
                "data support and analytical logic."
            ),
            "system_prompts": {
                "market": (
                    "You are a trading assistant tasked with analyzing financial markets. Your role is to select the **most relevant indicators** "
                    "for a given market condition or trading strategy from the following list. The goal is to choose up to **8 indicators** "
                    "that provide complementary insights without redundancy."
                ),
                "news": (
                    "You are a professional news analyst specializing in analyzing financial news and macroeconomic events' impact on stock prices. "
                    "Your task is to collect and analyze relevant news, assess their potential impact on the target company's stock price."
                ),
                "fundamentals": (
                    "You are a professional fundamental analyst responsible for analyzing company financial conditions and intrinsic value. "
                    "Your task is to conduct in-depth analysis of company financial data and assess its investment value."
                ),
                "sentiment": (
                    "You are a professional market sentiment analyst specializing in analyzing social media and online discussions' impact on stock prices. "
                    "Your task is to collect and analyze sentiment data from various social platforms and assess market sentiment trends."
                ),
                "bull_researcher": (
                    "You are a Bull Analyst advocating for investing in the stock. Your task is to build a strong, evidence-based case "
                    "emphasizing growth potential, competitive advantages, and positive market indicators. Leverage the provided research "
                    "and data to address concerns and counter bearish arguments effectively."
                ),
                "bear_researcher": (
                    "You are a Bear Analyst making the case against investing in the stock. Your goal is to present a well-reasoned argument "
                    "emphasizing risks, challenges, and negative indicators. Leverage the provided research and data to highlight potential "
                    "downsides and counter bullish arguments effectively."
                ),
                "default": (
                    "You are a professional financial analyst responsible for assisting with investment analysis tasks. "
                    "Please provide accurate, detailed analysis reports based on your expertise and available tools."
                )
            }
        }

    def get_financial_term(self, term: str) -> str:
        """
        获取金融术语的本地化翻译

        Args:
            term: 英文术语

        Returns:
            本地化的术语
        """
        return self.financial_terms[self.language].get(term.lower(), term)

    def format_prompt(self, agent_type: str, current_date: str, ticker: str, tool_names: str) -> str:
        """
        格式化完整的提示词

        Args:
            agent_type: 代理类型
            current_date: 当前日期
            ticker: 股票代码
            tool_names: 工具名称列表

        Returns:
            格式化的提示词
        """
        base_instruction = self.get_base_instruction()
        language_instruction = self.get_language_instruction()
        system_message = self.get_system_prompt(agent_type)

        if self.language == "chinese":
            date_text = f"当前日期是{current_date}。我们要分析的公司是{ticker}"
            tools_text = f"你可以使用以下工具：{tool_names}。"
        else:
            date_text = f"For your reference, the current date is {current_date}. The company we want to look at is {ticker}"
            tools_text = f"You have access to the following tools: {tool_names}."

        return f"{base_instruction} {tools_text}\n{language_instruction}\n{system_message}\n{date_text}"

    def format_researcher_prompt(self, researcher_type: str, market_report: str, sentiment_report: str,
                                news_report: str, fundamentals_report: str, history: str,
                                current_response: str, past_memory_str: str) -> str:
        """
        格式化研究员的提示词

        Args:
            researcher_type: 研究员类型 ("bull_researcher" 或 "bear_researcher")
            market_report: 市场研究报告
            sentiment_report: 情绪报告
            news_report: 新闻报告
            fundamentals_report: 基本面报告
            history: 辩论历史
            current_response: 当前回应
            past_memory_str: 过去记忆字符串

        Returns:
            格式化的研究员提示词
        """
        system_message = self.get_system_prompt(researcher_type)

        if self.language == "chinese":
            if researcher_type == "bull_researcher":
                prompt_template = f"""{system_message}

可用资源：

市场研究报告：{market_report}
社交媒体情绪报告：{sentiment_report}
最新世界事务新闻：{news_report}
公司基本面报告：{fundamentals_report}
辩论对话历史：{history}
最后的看跌论点：{current_response}
类似情况的反思和经验教训：{past_memory_str}

使用这些信息提出令人信服的看涨论点，反驳看跌担忧，并参与动态辩论，展示投资该股票的优势和潜力。
你还必须处理反思并从过去的经验教训和错误中学习。"""
            else:  # bear_researcher
                prompt_template = f"""{system_message}

可用资源：

市场研究报告：{market_report}
社交媒体情绪报告：{sentiment_report}
最新世界事务新闻：{news_report}
公司基本面报告：{fundamentals_report}
辩论对话历史：{history}
最后的看涨论点：{current_response}
类似情况的反思和经验教训：{past_memory_str}

使用这些信息提出令人信服的看跌论点，反驳看涨主张，并参与动态辩论，展示投资该股票的风险和弱点。
你还必须处理反思并从过去的经验教训和错误中学习。"""
        else:  # English
            if researcher_type == "bull_researcher":
                prompt_template = f"""{system_message}

Key points to focus on:
- Growth Potential: Highlight the company's market opportunities, revenue projections, and scalability.
- Competitive Advantages: Emphasize factors like unique products, strong branding, or dominant market positioning.
- Positive Indicators: Use financial health, industry trends, and recent positive news as evidence.
- Bear Counterpoints: Critically analyze the bear argument with specific data and sound reasoning, addressing concerns thoroughly and showing why the bull perspective holds stronger merit.
- Engagement: Present your argument in a conversational style, engaging directly with the bear analyst's points and debating effectively rather than just listing data.

Resources available:
Market research report: {market_report}
Social media sentiment report: {sentiment_report}
Latest world affairs news: {news_report}
Company fundamentals report: {fundamentals_report}
Conversation history of the debate: {history}
Last bear argument: {current_response}
Reflections from similar situations and lessons learned: {past_memory_str}

Use this information to deliver a compelling bull argument, refute the bear's concerns, and engage in a dynamic debate that demonstrates the strengths of the bull position. You must also address reflections and learn from lessons and mistakes you made in the past."""
            else:  # bear_researcher
                prompt_template = f"""{system_message}

Key points to focus on:
- Risks and Challenges: Highlight factors like market saturation, financial instability, or macroeconomic threats that could hinder the stock's performance.
- Competitive Weaknesses: Emphasize vulnerabilities such as weaker market positioning, declining innovation, or threats from competitors.
- Negative Indicators: Use evidence from financial data, market trends, or recent adverse news to support your position.
- Bull Counterpoints: Critically analyze the bull argument with specific data and sound reasoning, exposing weaknesses or over-optimistic assumptions.
- Engagement: Present your argument in a conversational style, directly engaging with the bull analyst's points and debating effectively rather than simply listing facts.

Resources available:
Market research report: {market_report}
Social media sentiment report: {sentiment_report}
Latest world affairs news: {news_report}
Company fundamentals report: {fundamentals_report}
Conversation history of the debate: {history}
Last bull argument: {current_response}
Reflections from similar situations and lessons learned: {past_memory_str}

Use this information to deliver a compelling bear argument, refute the bull's claims, and engage in a dynamic debate that demonstrates the risks and weaknesses of investing in the stock. You must also address reflections and learn from lessons and mistakes you made in the past."""

        return prompt_template
