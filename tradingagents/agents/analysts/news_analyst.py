from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json
from ..utils.prompt_manager import PromptManager


def create_news_analyst(llm, toolkit):
    def news_analyst_node(state):
        current_date = state["trade_date"]
        ticker = state["company_of_interest"]

        # 初始化提示词管理器
        language = toolkit.config.get("report_language", "chinese")
        prompt_manager = PromptManager(language)

        if toolkit.config["online_tools"]:
            tools = [toolkit.get_global_news_openai, toolkit.get_google_news]
        else:
            tools = [
                toolkit.get_finnhub_news,
                toolkit.get_reddit_news,
                toolkit.get_google_news,
            ]

        # 获取系统消息
        system_message = prompt_manager.get_system_prompt("news")

        # 使用提示词管理器格式化完整提示词
        tool_names = ", ".join([tool.name for tool in tools])
        formatted_system_prompt = prompt_manager.format_prompt(
            "news", current_date, ticker, tool_names
        )

        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", formatted_system_prompt),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        chain = prompt | llm.bind_tools(tools)
        result = chain.invoke(state["messages"])

        report = ""

        if len(result.tool_calls) == 0:
            report = result.content

        return {
            "messages": [result],
            "news_report": report,
        }

    return news_analyst_node
