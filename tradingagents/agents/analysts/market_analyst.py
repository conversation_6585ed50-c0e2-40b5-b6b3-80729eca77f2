from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json
from ..utils.prompt_manager import Prompt<PERSON>anager


def create_market_analyst(llm, toolkit):

    def market_analyst_node(state):
        current_date = state["trade_date"]
        ticker = state["company_of_interest"]
        company_name = state["company_of_interest"]

        # 初始化提示词管理器
        language = toolkit.config.get("report_language", "chinese")
        prompt_manager = PromptManager(language)

        if toolkit.config["online_tools"]:
            tools = [
                toolkit.get_YFin_data_online,
                toolkit.get_stockstats_indicators_report_online,
            ]
        else:
            tools = [
                toolkit.get_YFin_data,
                toolkit.get_stockstats_indicators_report,
            ]

        # 获取系统消息
        system_message = prompt_manager.get_system_prompt("market")

        # 使用提示词管理器格式化完整提示词
        tool_names = ", ".join([tool.name for tool in tools])
        formatted_system_prompt = prompt_manager.format_prompt(
            "market", current_date, ticker, tool_names
        )

        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", formatted_system_prompt),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        chain = prompt | llm.bind_tools(tools)

        result = chain.invoke(state["messages"])

        report = ""

        if len(result.tool_calls) == 0:
            report = result.content
       
        return {
            "messages": [result],
            "market_report": report,
        }

    return market_analyst_node
